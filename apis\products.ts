import apiClient from "@/lib/api/apiClient";
import { IMAGE_PLACEHOLDER_URL } from "@/constants/helpers";

// Cache for products to avoid unnecessary API calls
let productsCache: ProductData[] | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Map backend product model to frontend ProductData type
 */
const mapBackendProductToFrontend = (backendProduct: any): ProductData => {
  // Handle null or undefined backendProduct
  if (!backendProduct) {
    console.error("mapBackendProductToFrontend: Received null or undefined product");
    return {
      id: "",
      type: "buy",
      video: undefined,
      images: [IMAGE_PLACEHOLDER_URL],
      image: IMAGE_PLACEHOLDER_URL,
      title: "Product Not Found",
      name: "Product Not Found",
      category: "Uncategorized",
      available: false,
      discount: "0",
      price: 0,
      description: "This product could not be loaded.",
      quantity: 0,
      created: new Date().toISOString(),
    };
  }

  // Additional safety check for object type
  if (typeof backendProduct !== 'object') {
    console.error("mapBackendProductToFrontend: Received non-object product:", typeof backendProduct, backendProduct);
    return {
      id: "",
      type: "buy",
      video: undefined,
      images: [IMAGE_PLACEHOLDER_URL],
      image: IMAGE_PLACEHOLDER_URL,
      title: "Invalid Product Data",
      name: "Invalid Product Data",
      category: "Uncategorized",
      available: false,
      discount: "0",
      price: 0,
      description: "This product data is invalid.",
      quantity: 0,
      created: new Date().toISOString(),
    };
  }

  // Log the product data for debugging
  console.log("mapBackendProductToFrontend: Processing product:", backendProduct);

  return {
    id: backendProduct._id || backendProduct.id || "",
    type: backendProduct.type || "buy",
    video: backendProduct.video || undefined,
    images:
      backendProduct.images && backendProduct.images.length
        ? backendProduct.images
        : backendProduct.image
        ? [backendProduct.image]
        : [IMAGE_PLACEHOLDER_URL],
    image:
      backendProduct.image || (backendProduct.images && backendProduct.images[0]) || IMAGE_PLACEHOLDER_URL,
    title: backendProduct.name || "A Nice Product",
    name: backendProduct.name || "A Product",
    category: backendProduct.category || "Uncategorized",
    available: backendProduct.stock !== undefined ? backendProduct.stock : true,
    discount: backendProduct.discount || "0",
    price: backendProduct.price || 0,
    description: backendProduct.description,
    quantity: backendProduct.quantity,
    created: backendProduct.created,
  };
};

/**
 * Fetch all products from the backend API
 */
export const allProducts = async (): Promise<ProductData[]> => {
  const currentTime = Date.now();

  // Return cached products if they exist and are not expired
  if (productsCache && currentTime - lastFetchTime < CACHE_DURATION) {
    console.log("Using cached products data");
    return productsCache;
  }

  try {
    console.log("Fetching products from API");
    const response = await apiClient.get("/products");

    if (response.data && response.data.status === "success") {
      const backendProducts = response.data.data.products;
      const mappedProducts = backendProducts.map(mapBackendProductToFrontend);

      // Update cache
      productsCache = mappedProducts;
      lastFetchTime = currentTime;

      return mappedProducts;
    } else {
      console.error("API returned unexpected format:", response.data);
      return [];
    }
  } catch (error) {
    console.error("Error fetching products:", error);
    // Return empty array or cached data if available
    return productsCache || [];
  }
};

/**
 * Get featured products (top 6 products)
 */
export const featuredProducts = async (): Promise<ProductData[]> => {
  const allProductsData = await allProducts();
  // Return first 6 products or fewer if not enough products
  return allProductsData.slice(0, Math.min(6, allProductsData.length));
};

/**
 * Get new arrivals (most recent products based on created date)
 */
export const newArrivals = async (): Promise<ProductData[]> => {
  const allProductsData = await allProducts();
  // Sort by created date (newest first) and take first 6
  return [...allProductsData]
    .sort((a, b) => {
      const dateA = a.created ? new Date(a.created).getTime() : 0;
      const dateB = b.created ? new Date(b.created).getTime() : 0;
      return dateB - dateA;
    })
    .slice(0, Math.min(6, allProductsData.length));
};

/**
 * Get trending products (for now, just return a different set of products)
 */
export const trendingProducts = async (): Promise<ProductData[]> => {
  const allProductsData = await allProducts();
  // For now, just return a different subset of products
  // In a real app, this would be based on sales data or views
  return allProductsData
    .filter((_, index) => index % 2 === 0) // Just an example to get a different subset
    .slice(0, Math.min(6, allProductsData.length));
};

/**
 * Get products with optional filter
 */
export const getProducts = async (filter?: string): Promise<ProductData[]> => {
  try {
    switch (filter) {
      case "featured":
        return await featuredProducts();
      case "new":
        return await newArrivals();
      case "trending":
        return await trendingProducts();
      default:
        return await allProducts();
    }
  } catch (error) {
    console.error(`Error getting products with filter ${filter}:`, error);
    return [];
  }
};

/**
 * Get a single product by ID
 */
export const getProductById = async (
  id: string
): Promise<ProductData | undefined> => {
  try {
    // Validate ID format
    if (!id || typeof id !== 'string' || id.trim() === '') {
      console.error(`Invalid product ID: ${id}`);
      return undefined;
    }

    const trimmedId = id.trim();

    // Try to find in cache first
    const cachedProducts = productsCache;
    if (cachedProducts) {
      const cachedProduct = cachedProducts.find((p) => p.id === trimmedId);
      if (cachedProduct) {
        console.log(`Found product ${trimmedId} in cache`);
        return cachedProduct;
      }
    }

    // If not in cache, fetch from API
    console.log(`Fetching product ${trimmedId} from API`);
    const response = await apiClient.get(`/products/${trimmedId}`);

    if (response.data && response.data.status === "success") {
      // Log the full response for debugging
      console.log(`API response for product ${trimmedId}:`, JSON.stringify(response.data, null, 2));

      // Try both possible response structures
      const productData = response.data.data?.product || response.data.data;

      // More detailed logging for debugging
      console.log(`Extracted productData for ${trimmedId}:`, productData);
      console.log(`ProductData type:`, typeof productData);
      console.log(`ProductData has _id:`, productData && '_id' in productData);

      if (!productData) {
        console.error(`Product data is null/undefined for ID ${trimmedId}. Full response:`, response.data);
        return undefined;
      }

      if (!productData._id && !productData.id) {
        console.error(`Product with ID ${trimmedId} has no _id or id field. ProductData:`, productData);
        return undefined;
      }

      console.log(`Successfully fetched product ${trimmedId}:`, productData);
      return mapBackendProductToFrontend(productData);
    } else {
      console.error("API returned unexpected format:", response.data);
      return undefined;
    }
  } catch (error) {
    console.error(`Error fetching product with ID ${id}:`, error);

    // Check if it's a 404 error (product not found)
    if (error.response?.status === 404) {
      console.warn(`Product with ID ${id} does not exist (404)`);
      return undefined;
    }

    return undefined;
  }
};

/**
 * Get related products (products in the same category)
 */
export const getRelatedProducts = async (
  id: string,
  limit: number = 10
): Promise<ProductData[]> => {
  try {
    const product = await getProductById(id);
    if (!product) return [];

    const allProductsData = await allProducts();

    // Get products in the same category
    return allProductsData
      .filter((p) => p.id !== id && p.category === product.category)
      .slice(0, limit);
  } catch (error) {
    console.error(`Error fetching related products for ID ${id}:`, error);
    return [];
  }
};


