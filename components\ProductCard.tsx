"use client";
import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Heart, ShoppingCart, Eye } from "lucide-react";
import { toast } from "sonner";
import { useCart } from "@/contexts/CartContext";
import { useWishlist } from "@/contexts/WishlistContext";
import { useCurrency } from "@/contexts/CurrencyContext";
import ProductDetailModal from "./ProductDetailModal";
import { IMAGE_PLACEHOLDER_URL } from "@/constants/helpers";
import { ProductData } from "@/types";

interface ProductCardProps {
  product: ProductData;
  index?: number;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const [showModal, setShowModal] = useState(false);
  const { addToCart } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const { formatPrice, convertPrice } = useCurrency();

  // Check if product is in wishlist
  const productInWishlist = isInWishlist(product.id);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(product);
  };

  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (productInWishlist) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product.id);
    }
  };

  const handleQuickView = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowModal(true);
  };

  // Calculate discount price if available - same logic as product detail page
  const discountedPrice =
    product.discount && product.discount !== "0"
      ? Math.round(
          product.price - product.price * (parseInt(product.discount) / 100)
        )
      : null;

  return (
    <>
      {showModal && (
        <ProductDetailModal
          product={product}
          onClose={() => setShowModal(false)}
        />
      )}
      <div className="group relative">
        <Link href={`/products/${product.id}`}>
          <div className="relative overflow-hidden rounded-lg bg-white shadow-md hover:shadow-lg transition-all duration-300">
            {/* Discount badge */}
            {product.discount && product.discount !== "0" && (
              <div className="absolute top-3 left-3 z-10 bg-accent text-white text-xs font-bold px-2 py-1 rounded">
                {product.discount}% OFF
              </div>
            )}

            {/* Product image with enhanced optimization */}
            <div className="relative h-64 w-full overflow-hidden bg-gray-100">
              <Image
                src={
                  ((product.images && product.images.length > 0) ||
                  product.image
                    ? product.image || product.image
                    : IMAGE_PLACEHOLDER_URL) as string
                }
                alt={`${product.title || product.name} - Handcrafted ${product.category || 'wooden furniture'} from Chiniot, Punjab, Pakistan. Premium quality ${product.material || 'wood'} furniture with traditional craftsmanship and modern design. ${product.price ? `Price: PKR ${product.price.toLocaleString()}` : ''}`}
                fill
                className="object-cover object-center transition-transform duration-500 group-hover:scale-105"
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                loading="lazy"
                quality={85}
                placeholder="blur"
                blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Rj5m4xbDV9Qy9Qyq2jWMzKhPQdEVBBBB6EV9K9Dw=="
              />

              {/* Availability indicator */}
              {!product.available && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <span className="text-white font-semibold text-sm bg-red-600 px-3 py-1 rounded">
                    Out of Stock
                  </span>
                </div>
              )}
            </div>

            {/* Product info */}
            <div className="p-4">
              <h3 className="font-medium text-gray-900 mb-1 truncate" title={product.title || product.name}>
                {product.title || product.name || "Unnamed Product"}
              </h3>

              {/* Category and origin info */}
              <p className="text-xs text-gray-500 mb-2">
                {product.category && (
                  <span className="capitalize">{product.category}</span>
                )}
                {product.category && <span> • </span>}
                <span>Handcrafted in Chiniot</span>
              </p>

              {/* Price and Action Buttons Row */}
              <div className="flex items-center justify-between gap-2 ">
                {/* Price Section */}
                <div className="flex items-center flex-1 min-w-0 ">
                  {discountedPrice ? (
                    <>
                      <span className="text-accent font-semibold text-xs sm:text-base">
                        {formatPrice(discountedPrice)}
                      </span>
                      <span className="ml-1 sm:ml-2 text-gray-400 text-xs sm:text-sm line-through">
                        {formatPrice(product.price)}
                      </span>
                    </>
                  ) : (
                    <span className="text-accent font-semibold text-sm sm:text-base">
                      {formatPrice(product.price)}
                    </span>
                  )}

                  {!product.available && (
                    <span className="ml-1 sm:ml-2 text-xs text-red-500 font-medium truncate">
                      Out of stock
                    </span>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-3 sm:gap-1 flex-shrink-0">
                  <button
                    className="p-1 sm:p-1.5 rounded-full bg-gray-100 hover:bg-accent hover:text-white transition-colors"
                    onClick={handleQuickView}
                    aria-label="Quick view"
                  >
                    <Eye size={12} className="sm:w-3.5 sm:h-3.5" />
                  </button>
                  <button
                    className={`p-1 sm:p-1.5 rounded-full transition-colors ${
                      productInWishlist
                        ? "bg-red-500 text-white hover:bg-red-600"
                        : "bg-gray-100 text-gray-700 hover:bg-accent hover:text-white"
                    }`}
                    onClick={handleToggleWishlist}
                    aria-label={
                      productInWishlist
                        ? "Remove from wishlist"
                        : "Add to wishlist"
                    }
                  >
                    <Heart size={12} className="sm:w-3.5 sm:h-3.5" />
                  </button>
                  <button
                    className="p-1 sm:p-1.5 rounded-full bg-gray-100 hover:bg-accent hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={handleAddToCart}
                    aria-label="Add to cart"
                    disabled={!product.available}
                  >
                    <ShoppingCart size={12} className="sm:w-3.5 sm:h-3.5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Link>
      </div>
    </>
  );
};

export default ProductCard;
